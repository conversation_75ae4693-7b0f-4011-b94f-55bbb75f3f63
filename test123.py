import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

def test_proxy(proxy_address, timeout=10):
    """
    测试单个代理的可用性
    
    Args:
        proxy_address (str): 代理地址，格式为 "ip:port"
        timeout (int): 超时时间（秒）
    
    Returns:
        dict: 包含测试结果的字典
    """
    proxy_dict = {
        'http': f'http://{proxy_address}',
        'https': f'http://{proxy_address}'
    }
    
    result = {
        'proxy': proxy_address,
        'status': 'failed',
        'response_time': None,
        'ip_info': None,
        'error': None
    }
    
    try:
        start_time = time.time()
        
        # 测试方法1: 使用ip111.cn
        response = requests.get(
            'https://ip111.cn/',
            proxies=proxy_dict,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        end_time = time.time()
        response_time = round(end_time - start_time, 2)
        
        if response.status_code == 200:
            result['status'] = 'success'
            result['response_time'] = response_time
            result['ip_info'] = f"Status: {response.status_code}"
            
            # 尝试获取更详细的IP信息
            try:
                # 使用httpbin.org获取IP信息（更稳定的测试接口）
                ip_response = requests.get(
                    'http://httpbin.org/ip',
                    proxies=proxy_dict,
                    timeout=timeout
                )
                if ip_response.status_code == 200:
                    ip_data = ip_response.json()
                    result['ip_info'] = f"代理IP: {ip_data.get('origin', 'Unknown')}"
            except:
                pass
                
        else:
            result['error'] = f"HTTP状态码: {response.status_code}"
            
    except requests.exceptions.ConnectTimeout:
        result['error'] = "连接超时"
    except requests.exceptions.ProxyError:
        result['error'] = "代理错误"
    except requests.exceptions.ConnectionError:
        result['error'] = "连接错误"
    except Exception as e:
        result['error'] = f"其他错误: {str(e)}"
    
    return result

def test_multiple_proxies(proxy_list, max_workers=5, timeout=10):
    """
    并发测试多个代理
    
    Args:
        proxy_list (list): 代理地址列表
        max_workers (int): 最大并发数
        timeout (int): 超时时间
    
    Returns:
        list: 测试结果列表
    """
    results = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_proxy = {
            executor.submit(test_proxy, proxy, timeout): proxy 
            for proxy in proxy_list
        }
        
        # 收集结果
        for future in as_completed(future_to_proxy):
            result = future.result()
            results.append(result)
    
    return results

def print_results(results):
    """
    格式化打印测试结果
    """
    print("=" * 80)
    print("代理测试结果")
    print("=" * 80)
    
    for result in results:
        print(f"\n代理地址: {result['proxy']}")
        print(f"状态: {result['status']}")
        
        if result['status'] == 'success':
            print(f"响应时间: {result['response_time']}秒")
            if result['ip_info']:
                print(f"IP信息: {result['ip_info']}")
        else:
            print(f"错误信息: {result['error']}")
        
        print("-" * 50)
    
    # 统计信息
    success_count = sum(1 for r in results if r['status'] == 'success')
    total_count = len(results)
    
    print(f"\n总结:")
    print(f"测试总数: {total_count}")
    print(f"成功数量: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")

def main():
    # 要测试的代理列表
    proxy_list = [
        "**************:51012",
        "*************:53012"
    ]
    
    print("开始测试代理...")
    print(f"测试代理数量: {len(proxy_list)}")
    print(f"代理列表: {', '.join(proxy_list)}")
    
    # 执行测试
    results = test_multiple_proxies(proxy_list, timeout=15)
    
    # 打印结果
    print_results(results)

if __name__ == "__main__":
    main()
