import requests

def check_proxy(proxy):
    """
    检测代理 IP 是否可用，并验证访问的 IP 是否是通过代理 IP 进行访问的。
    
    :param proxy: 代理 IP 和端口，格式为 "host:port"，例如 "***********:8080"
    :return: 返回一个字典，包含检测结果
    """
    # 目标 URL，用于检测代理是否生效
    test_url = "http://httpbin.org/ip"
    
    # 本地 IP 地址，用于对比
    local_ip = requests.get(test_url).json()['origin']
    
    # 设置代理
    proxies = {
        "http": f"http://{proxy}",
        "https": f"https://{proxy}"
    }
    
    try:
        # 使用代理发送请求
        response = requests.get(test_url, proxies=proxies, timeout=5)
        
        # 获取响应中的 IP 地址
        proxy_ip = response.json()['origin']
        
        # 检测是否使用了代理
        if proxy_ip != local_ip:
            return {
                "status": "success",
                "message": "代理可用，访问的 IP 是通过代理 IP 进行访问的",
                "local_ip": local_ip,
                "proxy_ip": proxy_ip
            }
        else:
            return {
                "status": "failed",
                "message": "代理不可用，访问的 IP 与本地 IP 相同",
                "local_ip": local_ip,
                "proxy_ip": proxy_ip
            }
    except requests.exceptions.RequestException as e:
        # 捕获请求异常
        return {
            "status": "failed",
            "message": f"代理不可用，请求失败: {e}",
            "local_ip": local_ip,
            "proxy_ip": None
        }

# 示例：检测代理 IP
if __name__ == "__main__":
    proxy = input("请输入代理 IP 和端口（格式：host:port）：")
    result = check_proxy(proxy)
    print(result["message"])
    if result["status"] == "success":
        print(f"本地 IP: {result['local_ip']}")
        print(f"代理 IP: {result['proxy_ip']}")