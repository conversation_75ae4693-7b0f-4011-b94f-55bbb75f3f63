import requests
import time
import subprocess
import random

class LDPlayerProxyManager:
    def __init__(self):
        self.ld_console_path = r"F:\leidian\LDPlayer9\ldconsole.exe"  # 雷电控制台路径
        self.device_name = "雷电模拟器"  # 模拟器名称
        
    def get_proxy_ip(self):
        """从代理服务商获取新的IP"""
        # 这里替换为您的代理API
        api_url = "http://**************:5010/get/"
        try:
            response = requests.get(api_url)
            proxy_data = response.json()
            return proxy_data['proxy']
        except:
            return None, None
    
    def set_proxy_in_emulator(self, ip, port):
        """在模拟器中设置代理"""
        # 方法1: 通过ADB命令设置系统代理
        adb_command = f'adb shell settings put global http_proxy {ip}:{port}'
        subprocess.run(adb_command, shell=True)
        
    def restart_network(self):
        """重启网络连接"""
        subprocess.run('adb shell svc wifi disable', shell=True)
        time.sleep(2)
        subprocess.run('adb shell svc wifi enable', shell=True)
        time.sleep(5)
    
    def change_ip(self):
        """更换IP的主流程"""
        proxy = self.get_proxy_ip()
        if ip and port:
            print(f"获取到新IP: {proxy}")
            self.set_proxy_in_emulator(ip, port)
            self.restart_network()
            print("IP更换完成")
            return True
        return False
    
    def run_auto_change(self):
        """每5分钟自动更换IP"""
        while True:
            try:
                if self.change_ip():
                    print("等待5分钟后再次更换...")
                    time.sleep(300)  # 5分钟
                else:
                    print("获取代理IP失败，30秒后重试...")
                    time.sleep(30)
            except Exception as e:
                print(f"发生错误: {e}")
                time.sleep(60)

if __name__ == "__main__":
    manager = LDPlayerProxyManager()
    manager.run_auto_change()
