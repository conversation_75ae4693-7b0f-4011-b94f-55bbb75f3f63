import requests
import time
import subprocess
import random

class LDPlayerProxyManager:
    def __init__(self):
        self.ld_console_path = r"F:\leidian\LDPlayer9\ldconsole.exe"  # 雷电控制台路径
        self.device_name = "雷电模拟器"  # 模拟器名称
        
    def get_proxy_ip(self):
        """从代理服务商获取新的IP"""
        # 这里替换为您的代理API
        api_url = "http://**************:5010/get/"
        try:
            response = requests.get(api_url)
            proxy_data = response.json()
            return proxy_data['proxy']
        except:
            return None, None
    
    def set_proxy_in_emulator(self, proxy):
        """在模拟器中设置代理"""
        try:
            # 方法1: 通过ADB命令设置系统代理
            # 设置HTTP代理
            adb_command = f'adb shell settings put global http_proxy {proxy}'
            result = subprocess.run(adb_command, shell=True, capture_output=True, text=True)
            print(f"设置HTTP代理: {adb_command}")
            if result.returncode != 0:
                print(f"HTTP代理设置失败: {result.stderr}")
                return False

            # 验证代理设置
            verify_command = 'adb shell settings get global http_proxy'
            verify_result = subprocess.run(verify_command, shell=True, capture_output=True, text=True)
            if verify_result.returncode == 0:
                current_proxy = verify_result.stdout.strip()
                print(f"当前代理设置: {current_proxy}")
                return current_proxy == proxy
            else:
                print("无法验证代理设置")
                return False

        except Exception as e:
            print(f"设置代理时发生错误: {e}")
            return False

    def clear_proxy(self):
        """清除代理设置"""
        try:
            # 清除HTTP代理设置
            clear_command = 'adb shell settings put global http_proxy :0'
            result = subprocess.run(clear_command, shell=True, capture_output=True, text=True)
            print("清除代理设置")
            if result.returncode == 0:
                print("代理设置已清除")
                return True
            else:
                print(f"清除代理失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"清除代理时发生错误: {e}")
            return False

    def restart_network(self):
        """重启网络连接"""
        subprocess.run('adb shell svc wifi disable', shell=True)
        time.sleep(2)
        subprocess.run('adb shell svc wifi enable', shell=True)
        time.sleep(5)
    
    def change_ip(self):
        """更换IP的主流程"""
        proxy = self.get_proxy_ip()
        if proxy:
            print(f"获取到新IP: {proxy}")
            # 设置代理
            if self.set_proxy_in_emulator(proxy):
                self.restart_network()
                print("IP更换完成")
                return True
            else:
                print("代理设置失败")
                return False
        else:
            print("获取代理IP失败")
            return False
    
    def run_auto_change(self):
        """每5分钟自动更换IP"""
        while True:
            try:
                if self.change_ip():
                    print("等待5分钟后再次更换...")
                    time.sleep(300)  # 5分钟
                else:
                    print("获取代理IP失败，30秒后重试...")
                    time.sleep(30)
            except Exception as e:
                print(f"发生错误: {e}")
                time.sleep(60)

if __name__ == "__main__":
    manager = LDPlayerProxyManager()
    manager.run_auto_change()
