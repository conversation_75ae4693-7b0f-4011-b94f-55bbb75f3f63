import requests
import time
import subprocess
import random

class LDPlayerProxyManager:
    def __init__(self):
        self.ld_console_path = r"F:\leidian\LDPlayer9\ldconsole.exe"  # 雷电控制台路径
        self.adb_path = r"F:\leidian\LDPlayer9\adb.exe"  # 雷电ADB路径
        self.device_name = "雷电模拟器"  # 模拟器名称
        
    def get_proxy_ip(self):
        """从代理服务商获取新的IP"""
        # 这里替换为您的代理API
        api_url = "http://**************:5010/get/"
        try:
            response = requests.get(api_url)
            proxy_data = response.json()
            return proxy_data['proxy']
        except Exception as e:
            print(f"获取代理IP失败: {e}")
            return None
    
    def set_proxy_in_emulator(self, proxy):
        """在模拟器中设置代理"""
        try:
            # 方法1: 通过ADB命令设置系统代理
            # 设置HTTP代理
            adb_command = f'"{self.adb_path}" shell settings put global http_proxy {proxy}'
            result = subprocess.run(adb_command, shell=True, capture_output=True, text=True)
            print(f"设置HTTP代理: {adb_command}")
            if result.returncode != 0:
                print(f"HTTP代理设置失败: {result.stderr}")
                return False

            # 验证代理设置
            verify_command = f'"{self.adb_path}" shell settings get global http_proxy'
            verify_result = subprocess.run(verify_command, shell=True, capture_output=True, text=True)
            if verify_result.returncode == 0:
                current_proxy = verify_result.stdout.strip()
                print(f"当前代理设置: {current_proxy}")
                return current_proxy == proxy
            else:
                print("无法验证代理设置")
                return False

        except Exception as e:
            print(f"设置代理时发生错误: {e}")
            return False

    def clear_proxy(self):
        """清除代理设置"""
        try:
            # 清除HTTP代理设置
            clear_command = f'"{self.adb_path}" shell settings put global http_proxy :0'
            result = subprocess.run(clear_command, shell=True, capture_output=True, text=True)
            print("清除代理设置")
            if result.returncode == 0:
                print("代理设置已清除")
                return True
            else:
                print(f"清除代理失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"清除代理时发生错误: {e}")
            return False
    
    def test_proxy(self):
        """测试代理是否生效"""
        try:
            # 在模拟器中执行curl命令测试IP
            test_command = f'"{self.adb_path}" shell "curl -s http://httpbin.org/ip"'
            result = subprocess.run(test_command, shell=True, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"当前IP测试结果: {result.stdout.strip()}")
                return True
            else:
                print(f"IP测试失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"测试代理时发生错误: {e}")
            return False

    def restart_network(self):
        """重启网络连接"""
        subprocess.run(f'"{self.adb_path}" shell svc wifi disable', shell=True)
        time.sleep(2)
        subprocess.run(f'"{self.adb_path}" shell svc wifi enable', shell=True)
        time.sleep(5)
    
    def test_single_proxy_change(self):
        """测试单次代理更换"""
        print("=== 开始测试代理设置 ===")
        
        # 1. 获取新的代理IP
        proxy = self.get_proxy_ip()
        if not proxy:
            print("❌ 无法获取代理IP")
            return False
            
        print(f"✅ 获取到新IP: {proxy}")
        
        # 2. 设置代理
        if self.set_proxy_in_emulator(proxy):
            print("✅ 代理设置成功")
        else:
            print("❌ 代理设置失败")
            return False
            
        # 3. 重启网络
        print("🔄 重启网络连接...")
        self.restart_network()
        
        # 4. 测试代理是否生效
        print("🧪 测试代理是否生效...")
        if self.test_proxy():
            print("✅ 代理测试成功")
        else:
            print("⚠️ 代理测试失败，但这可能是因为模拟器中没有curl命令")
            
        print("=== 测试完成 ===")
        print("现在您可以在雷电模拟器中打开浏览器测试IP是否已更换")
        return True

if __name__ == "__main__":
    manager = LDPlayerProxyManager()
    manager.test_single_proxy_change()
